<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/authStore'
import AppSidebar from './AppSidebar.vue'
import AppHeader from './AppHeader.vue'
import AppFooter from './AppFooter.vue'
import AppNotification from '@/components/ui/AppNotification.vue'

const authStore = useAuthStore()

onMounted(() => {
  // 初始化认证状态
  authStore.initAuth()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 flex">
    <!-- 侧边栏 -->
    <AppSidebar />

    <!-- 主内容区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 顶部栏 -->
      <AppHeader />

      <!-- 页面内容 -->
      <main class="flex-1 p-6 overflow-auto">
        <div class="max-w-7xl mx-auto">
          <router-view />
        </div>
      </main>

      <!-- 页脚 -->
      <AppFooter />
    </div>

    <!-- 全局通知 -->
    <AppNotification />
  </div>
</template>
