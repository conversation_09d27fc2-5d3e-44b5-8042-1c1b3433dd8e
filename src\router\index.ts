import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: { requiresAuth: true },
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/auth/LoginView.vue'),
      meta: { requiresAuth: false },
    },
    {
      path: '/info',
      name: 'info',
      component: () => import('../views/info/InfoView.vue'),
      meta: { requiresAuth: true, permission: 'info:read' },
    },
    {
      path: '/info/:id',
      name: 'info-detail',
      component: () => import('../views/info/InfoView.vue'),
      meta: { requiresAuth: true, permission: 'info:read' },
    },
    {
      path: '/study',
      name: 'study',
      component: () => import('../views/study/StudyView.vue'),
      meta: { requiresAuth: true, permission: 'study:read' },
    },
    {
      path: '/exam',
      name: 'exam',
      component: () => import('../views/exam/ExamView.vue'),
      meta: { requiresAuth: true, permission: 'exam:read' },
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/profile/ProfileView.vue'),
      meta: { requiresAuth: true, permission: 'profile:read' },
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: { requiresAuth: false },
    },
    // 404 页面
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/common/NotFoundView.vue'),
      meta: { requiresAuth: false },
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // 未登录，重定向到登录页
      next({ name: 'login', query: { redirect: to.fullPath } })
      return
    }

    // 检查权限
    if (to.meta.permission && !authStore.hasPermission(to.meta.permission as string)) {
      // 权限不足，重定向到首页
      next({ name: 'home' })
      return
    }
  }

  // 如果已登录用户访问登录页，重定向到首页
  if (to.name === 'login' && authStore.isAuthenticated) {
    next({ name: 'home' })
    return
  }

  next()
})

export default router
