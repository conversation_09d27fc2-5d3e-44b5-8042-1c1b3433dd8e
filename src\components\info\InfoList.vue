<script setup lang="ts">
import { computed } from 'vue'
import { useInfo } from '@/composables/useInfo'
import type { InfoItem } from '@/types'
import InfoCard from './InfoCard.vue'
import InfoPagination from './InfoPagination.vue'
import InfoSkeleton from './InfoSkeleton.vue'

const {
  infoList,
  pagination,
  isLoadingList,
  changePage,
  viewInfoDetail,
  formatDate,
  getTypeLabel,
  getTypeColor
} = useInfo()

// 计算属性
const hasData = computed(() => infoList.value.length > 0)
const isEmpty = computed(() => !isLoadingList.value && !hasData.value)

// 方法
function handleViewDetail(info: InfoItem) {
  viewInfoDetail(info.id)
}

function handlePageChange(page: number) {
  changePage(page)
}
</script>

<template>
  <div class="space-y-6">
    <!-- 加载状态 -->
    <div v-if="isLoadingList" class="space-y-4">
      <InfoSkeleton v-for="i in 5" :key="i" />
    </div>

    <!-- 信息列表 -->
    <div v-else-if="hasData" class="space-y-4">
      <InfoCard
        v-for="info in infoList"
        :key="info.id"
        :info="info"
        @click="handleViewDetail(info)"
      />
    </div>

    <!-- 空状态 -->
    <div v-else-if="isEmpty" class="text-center py-12">
      <div class="text-6xl mb-4">📭</div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无信息</h3>
      <p class="text-gray-500">当前没有符合条件的信息，请尝试调整筛选条件</p>
    </div>

    <!-- 分页 -->
    <InfoPagination
      v-if="hasData && pagination.totalPages > 1"
      :current-page="pagination.page"
      :total-pages="pagination.totalPages"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      @page-change="handlePageChange"
    />
  </div>
</template>

<style scoped>
/* 组件样式 */
</style>
