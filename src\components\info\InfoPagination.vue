<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  currentPage: number
  totalPages: number
  total: number
  pageSize: number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  pageChange: [page: number]
}>()

// 计算显示的页码范围
const visiblePages = computed(() => {
  const delta = 2 // 当前页前后显示的页数
  const range = []
  const rangeWithDots = []

  for (let i = Math.max(2, props.currentPage - delta); 
       i <= Math.min(props.totalPages - 1, props.currentPage + delta); 
       i++) {
    range.push(i)
  }

  if (props.currentPage - delta > 2) {
    rangeWithDots.push(1, '...')
  } else {
    rangeWithDots.push(1)
  }

  rangeWithDots.push(...range)

  if (props.currentPage + delta < props.totalPages - 1) {
    rangeWithDots.push('...', props.totalPages)
  } else if (props.totalPages > 1) {
    rangeWithDots.push(props.totalPages)
  }

  return rangeWithDots
})

// 计算显示的数据范围
const dataRange = computed(() => {
  const start = (props.currentPage - 1) * props.pageSize + 1
  const end = Math.min(props.currentPage * props.pageSize, props.total)
  return { start, end }
})

// 方法
function goToPage(page: number | string) {
  if (typeof page === 'number' && page !== props.currentPage && page >= 1 && page <= props.totalPages) {
    emit('pageChange', page)
  }
}

function goToPrevious() {
  if (props.currentPage > 1) {
    goToPage(props.currentPage - 1)
  }
}

function goToNext() {
  if (props.currentPage < props.totalPages) {
    goToPage(props.currentPage + 1)
  }
}
</script>

<template>
  <div class="flex items-center justify-between bg-white px-4 py-3 border border-gray-200 rounded-lg">
    <!-- 数据统计信息 -->
    <div class="flex items-center text-sm text-gray-700">
      <span>
        显示第 <span class="font-medium">{{ dataRange.start }}</span> 到 
        <span class="font-medium">{{ dataRange.end }}</span> 条，
        共 <span class="font-medium">{{ total }}</span> 条记录
      </span>
    </div>

    <!-- 分页控件 -->
    <div class="flex items-center space-x-2">
      <!-- 上一页 -->
      <button
        :disabled="currentPage <= 1"
        :class="[
          'relative inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
          currentPage <= 1 
            ? 'text-gray-400 cursor-not-allowed' 
            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
        ]"
        @click="goToPrevious"
      >
        <span class="sr-only">上一页</span>
        ← 上一页
      </button>

      <!-- 页码 -->
      <div class="flex items-center space-x-1">
        <template v-for="(page, index) in visiblePages" :key="index">
          <!-- 省略号 -->
          <span 
            v-if="page === '...'"
            class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700"
          >
            ...
          </span>
          
          <!-- 页码按钮 -->
          <button
            v-else
            :class="[
              'relative inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
              page === currentPage
                ? 'bg-blue-600 text-white'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
            ]"
            @click="goToPage(page as number)"
          >
            {{ page }}
          </button>
        </template>
      </div>

      <!-- 下一页 -->
      <button
        :disabled="currentPage >= totalPages"
        :class="[
          'relative inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
          currentPage >= totalPages 
            ? 'text-gray-400 cursor-not-allowed' 
            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
        ]"
        @click="goToNext"
      >
        <span class="sr-only">下一页</span>
        下一页 →
      </button>
    </div>
  </div>
</template>

<style scoped>
/* 分页样式 */
button:disabled {
  @apply cursor-not-allowed opacity-50;
}
</style>
