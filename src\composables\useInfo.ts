import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query'
import { useInfoStore } from '@/stores/infoStore'
import { useNotificationStore } from '@/stores/notificationStore'
import type { InfoItem, PaginationParams, SearchParams } from '@/types'
import * as infoApi from '@/api/info'

export function useInfo() {
  const infoStore = useInfoStore()
  const notificationStore = useNotificationStore()
  const router = useRouter()
  const queryClient = useQueryClient()

  // 响应式状态
  const currentPage = ref(1)
  const pageSize = ref(10)
  const searchKeyword = ref('')
  const selectedCategory = ref<InfoItem['type'] | ''>('')
  const selectedStatus = ref<InfoItem['status'] | ''>('published')

  // 计算属性
  const queryParams = computed(() => ({
    page: currentPage.value,
    pageSize: pageSize.value,
    keyword: searchKeyword.value,
    category: selectedCategory.value,
    status: selectedStatus.value
  }))

  // 使用 TanStack Query 管理数据获取
  const {
    data: infoListData,
    isLoading: isLoadingList,
    error: listError,
    refetch: refetchList
  } = useQuery({
    queryKey: ['infoList', queryParams],
    queryFn: () => infoStore.fetchInfoList(queryParams.value),
    staleTime: 1000 * 60 * 5, // 5分钟
    enabled: true
  })

  const {
    data: infoStats,
    isLoading: isLoadingStats
  } = useQuery({
    queryKey: ['infoStats'],
    queryFn: () => infoApi.getInfoStats(),
    staleTime: 1000 * 60 * 10 // 10分钟
  })

  const {
    data: popularInfo,
    isLoading: isLoadingPopular
  } = useQuery({
    queryKey: ['popularInfo'],
    queryFn: () => infoApi.getPopularInfo(5),
    staleTime: 1000 * 60 * 15 // 15分钟
  })

  const {
    data: latestInfo,
    isLoading: isLoadingLatest
  } = useQuery({
    queryKey: ['latestInfo'],
    queryFn: () => infoApi.getLatestInfo(5),
    staleTime: 1000 * 60 * 5 // 5分钟
  })

  // 获取信息详情的 mutation
  const infoDetailMutation = useMutation({
    mutationFn: (id: string) => infoStore.fetchInfoDetail(id),
    onSuccess: (data) => {
      // 更新缓存
      queryClient.setQueryData(['infoDetail', data.id], data)
    },
    onError: (error) => {
      notificationStore.error('获取详情失败', '无法加载信息详情，请稍后重试')
      console.error('获取信息详情失败:', error)
    }
  })

  // 增加浏览量的 mutation
  const incrementViewsMutation = useMutation({
    mutationFn: (id: string) => infoApi.incrementInfoViews(id),
    onSuccess: (_, id) => {
      // 更新本地缓存中的浏览量
      queryClient.invalidateQueries({ queryKey: ['infoList'] })
      queryClient.invalidateQueries({ queryKey: ['infoDetail', id] })
    }
  })

  // 方法
  function updateSearch(params: Partial<SearchParams>) {
    if (params.keyword !== undefined) searchKeyword.value = params.keyword
    if (params.category !== undefined) selectedCategory.value = params.category as InfoItem['type'] | ''
    if (params.status !== undefined) selectedStatus.value = params.status as InfoItem['status'] | ''
    
    // 重置到第一页
    currentPage.value = 1
  }

  function resetSearch() {
    searchKeyword.value = ''
    selectedCategory.value = ''
    selectedStatus.value = 'published'
    currentPage.value = 1
  }

  function changePage(page: number) {
    currentPage.value = page
  }

  function changePageSize(size: number) {
    pageSize.value = size
    currentPage.value = 1
  }

  async function viewInfoDetail(id: string) {
    try {
      await infoDetailMutation.mutateAsync(id)
      // 异步增加浏览量
      incrementViewsMutation.mutate(id)
      // 导航到详情页
      router.push(`/info/${id}`)
    } catch (error) {
      // 错误已在 mutation 中处理
    }
  }

  function downloadAttachment(attachmentId: string, fileName: string) {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = `/api/info/attachment/${attachmentId}/download`
    link.download = fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    notificationStore.success('下载开始', `正在下载 ${fileName}`)
  }

  // 格式化辅助函数
  function formatDate(dateString: string): string {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  function getTypeLabel(type: InfoItem['type']): string {
    const typeLabels = {
      announcement: '公告',
      policy: '政策',
      notification: '通知'
    }
    return typeLabels[type] || type
  }

  function getStatusLabel(status: InfoItem['status']): string {
    const statusLabels = {
      published: '已发布',
      draft: '草稿'
    }
    return statusLabels[status] || status
  }

  function getTypeColor(type: InfoItem['type']): string {
    const typeColors = {
      announcement: 'bg-blue-100 text-blue-800',
      policy: 'bg-green-100 text-green-800',
      notification: 'bg-yellow-100 text-yellow-800'
    }
    return typeColors[type] || 'bg-gray-100 text-gray-800'
  }

  // 监听搜索参数变化，自动刷新数据
  watch(queryParams, () => {
    refetchList()
  }, { deep: true })

  return {
    // 状态
    infoList: computed(() => infoStore.infoList),
    currentInfo: computed(() => infoStore.currentInfo),
    pagination: computed(() => infoStore.pagination),
    isLoadingList,
    isLoadingStats,
    isLoadingPopular,
    isLoadingLatest,
    isLoadingDetail: computed(() => infoDetailMutation.isPending),
    
    // 搜索状态
    currentPage,
    pageSize,
    searchKeyword,
    selectedCategory,
    selectedStatus,
    
    // 数据
    infoStats,
    popularInfo,
    latestInfo,
    
    // 方法
    updateSearch,
    resetSearch,
    changePage,
    changePageSize,
    viewInfoDetail,
    downloadAttachment,
    refetchList,
    
    // 格式化方法
    formatDate,
    getTypeLabel,
    getStatusLabel,
    getTypeColor
  }
}

// 专门用于信息详情页的 composable
export function useInfoDetail(id: string) {
  const infoStore = useInfoStore()
  const notificationStore = useNotificationStore()

  const {
    data: infoDetail,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['infoDetail', id],
    queryFn: () => infoStore.fetchInfoDetail(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 10 // 10分钟
  })

  // 增加浏览量
  const incrementViewsMutation = useMutation({
    mutationFn: () => infoApi.incrementInfoViews(id),
    onError: (error) => {
      console.error('增加浏览量失败:', error)
    }
  })

  // 在组件挂载时增加浏览量
  function trackView() {
    incrementViewsMutation.mutate()
  }

  return {
    infoDetail,
    isLoading,
    error,
    refetch,
    trackView
  }
}
