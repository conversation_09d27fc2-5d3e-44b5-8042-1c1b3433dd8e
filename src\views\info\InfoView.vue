<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useInfo } from '@/composables/useInfo'
import InfoFilter from '@/components/info/InfoFilter.vue'
import InfoList from '@/components/info/InfoList.vue'
import InfoDetail from '@/components/info/InfoDetail.vue'

const route = useRoute()

// 使用信息管理 composable
const {
  infoStats,
  isLoadingStats,
  popularInfo,
  isLoadingPopular,
  latestInfo,
  isLoadingLatest,
  refetchList,
} = useInfo()

// 计算属性
const isDetailView = computed(() => !!route.params.id)
const pageTitle = computed(() => (isDetailView.value ? '信息详情' : '信息中心'))

// 生命周期
onMounted(() => {
  // 初始化数据
  refetchList()
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">{{ pageTitle }}</h1>
        <p v-if="!isDetailView" class="mt-1 text-sm text-gray-600">
          查看最新的公告、政策和通知信息
        </p>
      </div>

      <!-- 统计信息 -->
      <div v-if="!isDetailView && infoStats" class="flex items-center space-x-6 text-sm">
        <div class="text-center">
          <div class="text-lg font-semibold text-blue-600">{{ infoStats.total }}</div>
          <div class="text-gray-500">总信息</div>
        </div>
        <div class="text-center">
          <div class="text-lg font-semibold text-green-600">{{ infoStats.announcement }}</div>
          <div class="text-gray-500">公告</div>
        </div>
        <div class="text-center">
          <div class="text-lg font-semibold text-purple-600">{{ infoStats.policy }}</div>
          <div class="text-gray-500">政策</div>
        </div>
        <div class="text-center">
          <div class="text-lg font-semibold text-orange-600">{{ infoStats.notification }}</div>
          <div class="text-gray-500">通知</div>
        </div>
      </div>
    </div>

    <!-- 信息详情页面 -->
    <InfoDetail v-if="isDetailView" />

    <!-- 信息列表页面 -->
    <template v-else>
      <!-- 快捷信息区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- 热门信息 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center space-x-2">
            <span>🔥</span>
            <span>热门信息</span>
          </h3>

          <div v-if="isLoadingPopular" class="space-y-3">
            <div v-for="i in 3" :key="i" class="animate-pulse">
              <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div class="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>

          <div v-else-if="popularInfo && popularInfo.length > 0" class="space-y-3">
            <div
              v-for="info in popularInfo.slice(0, 5)"
              :key="info.id"
              class="cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors"
              @click="$router.push(`/info/${info.id}`)"
            >
              <h4 class="text-sm font-medium text-gray-900 truncate">{{ info.title }}</h4>
              <div class="flex items-center space-x-2 mt-1 text-xs text-gray-500">
                <span>👁️ {{ info.views }}</span>
                <span>•</span>
                <span>{{ new Date(info.publishTime).toLocaleDateString('zh-CN') }}</span>
              </div>
            </div>
          </div>

          <div v-else class="text-center text-gray-500 py-4">暂无热门信息</div>
        </div>

        <!-- 最新信息 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center space-x-2">
            <span>🆕</span>
            <span>最新信息</span>
          </h3>

          <div v-if="isLoadingLatest" class="space-y-3">
            <div v-for="i in 3" :key="i" class="animate-pulse">
              <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div class="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>

          <div v-else-if="latestInfo && latestInfo.length > 0" class="space-y-3">
            <div
              v-for="info in latestInfo.slice(0, 5)"
              :key="info.id"
              class="cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors"
              @click="$router.push(`/info/${info.id}`)"
            >
              <h4 class="text-sm font-medium text-gray-900 truncate">{{ info.title }}</h4>
              <div class="flex items-center space-x-2 mt-1 text-xs text-gray-500">
                <span
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800"
                >
                  {{
                    info.type === 'announcement' ? '公告' : info.type === 'policy' ? '政策' : '通知'
                  }}
                </span>
                <span>•</span>
                <span>{{ new Date(info.publishTime).toLocaleDateString('zh-CN') }}</span>
              </div>
            </div>
          </div>

          <div v-else class="text-center text-gray-500 py-4">暂无最新信息</div>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <InfoFilter />

      <!-- 信息列表 -->
      <InfoList />
    </template>
  </div>
</template>
