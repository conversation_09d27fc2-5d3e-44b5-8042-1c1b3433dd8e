<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { useNotificationStore } from '@/stores/notificationStore'
import AppButton from '@/components/ui/AppButton.vue'
import AppInput from '@/components/ui/AppInput.vue'

const router = useRouter()
const authStore = useAuthStore()
const notificationStore = useNotificationStore()

const isLoading = ref(false)
const loginForm = ref({
  username: '',
  password: ''
})

async function handleLogin() {
  if (!loginForm.value.username || !loginForm.value.password) {
    notificationStore.warning('请填写完整信息', '用户名和密码不能为空')
    return
  }

  isLoading.value = true
  
  try {
    // 模拟登录API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟登录成功
    const mockUser = {
      id: '1',
      username: loginForm.value.username,
      realName: '张三',
      workUnit: '某疾控中心',
      position: '医师',
      permissions: ['info:read', 'study:read', 'exam:read', 'profile:read'],
      email: '<EMAIL>',
      phone: '13800138000',
      idCard: '110101199001011234',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    authStore.setAuth({
      token: 'mock-jwt-token',
      user: mockUser,
      expiresIn: 3600
    })
    
    notificationStore.success('登录成功', '欢迎使用疾控医护任职资格考试系统')
    router.push('/')
    
  } catch (error) {
    notificationStore.error('登录失败', '用户名或密码错误')
  } finally {
    isLoading.value = false
  }
}

function handleWeChatLogin() {
  notificationStore.info('微信登录', '微信扫码登录功能正在开发中...')
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div class="text-center">
        <div class="mx-auto w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
          <span class="text-white font-bold text-xl">CDC</span>
        </div>
        <h2 class="text-3xl font-bold text-gray-900">疾控考试系统</h2>
        <p class="mt-2 text-gray-600">医护任职资格考试平台</p>
      </div>

      <!-- 登录表单 -->
      <div class="bg-white rounded-lg shadow-md p-8">
        <form @submit.prevent="handleLogin" class="space-y-6">
          <AppInput
            v-model="loginForm.username"
            label="用户名"
            placeholder="请输入用户名"
            required
          />
          
          <AppInput
            v-model="loginForm.password"
            type="password"
            label="密码"
            placeholder="请输入密码"
            required
          />

          <div class="flex items-center justify-between">
            <label class="flex items-center">
              <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="ml-2 text-sm text-gray-600">记住我</span>
            </label>
            <a href="#" class="text-sm text-blue-600 hover:text-blue-500">忘记密码？</a>
          </div>

          <AppButton
            type="submit"
            :loading="isLoading"
            class="w-full"
          >
            登录
          </AppButton>
        </form>

        <!-- 分割线 -->
        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">或</span>
            </div>
          </div>
        </div>

        <!-- 微信登录 -->
        <div class="mt-6">
          <AppButton
            variant="secondary"
            class="w-full"
            @click="handleWeChatLogin"
          >
            <span class="mr-2">📱</span>
            微信扫码登录
          </AppButton>
        </div>
      </div>

      <!-- 底部链接 -->
      <div class="text-center text-sm text-gray-600">
        <p>
          还没有账号？
          <router-link to="/register" class="text-blue-600 hover:text-blue-500">立即注册</router-link>
        </p>
      </div>
    </div>
  </div>
</template>
