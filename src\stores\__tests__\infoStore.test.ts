import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON>inia, createPinia } from 'pinia'
import { useInfoStore } from '../infoStore'
import type { InfoItem } from '@/types'

// Mock API
vi.mock('@/api', () => ({
  request: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}))

describe('InfoStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('应该正确初始化状态', () => {
    const store = useInfoStore()

    expect(store.infoList).toEqual([])
    expect(store.currentInfo).toBeNull()
    expect(store.isLoading).toBe(false)
    expect(store.isLoadingDetail).toBe(false)
    expect(store.pagination).toEqual({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0,
    })
    expect(store.searchParams).toEqual({
      keyword: '',
      category: '',
      status: 'published',
    })
  })

  it('应该正确计算筛选后的信息列表', () => {
    const store = useInfoStore()

    // 设置测试数据
    store.infoList = [
      {
        id: '1',
        title: '测试公告',
        content: '公告内容',
        type: 'announcement',
        status: 'published',
        author: '管理员',
        publishTime: '2024-01-15T10:00:00Z',
        views: 100,
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
      },
      {
        id: '2',
        title: '测试政策',
        content: '政策内容',
        type: 'policy',
        status: 'published',
        author: '政策部',
        publishTime: '2024-01-16T10:00:00Z',
        views: 200,
        createdAt: '2024-01-16T10:00:00Z',
        updatedAt: '2024-01-16T10:00:00Z',
      },
      {
        id: '3',
        title: '草稿通知',
        content: '通知内容',
        type: 'notification',
        status: 'draft',
        author: '编辑',
        publishTime: '2024-01-17T10:00:00Z',
        views: 50,
        createdAt: '2024-01-17T10:00:00Z',
        updatedAt: '2024-01-17T10:00:00Z',
      },
    ] as InfoItem[]

    // 测试按类型筛选
    store.searchParams.category = 'announcement'
    expect(store.filteredInfoList).toHaveLength(1)
    expect(store.filteredInfoList[0].type).toBe('announcement')

    // 测试按状态筛选
    store.searchParams.category = ''
    store.searchParams.status = 'draft'
    expect(store.filteredInfoList).toHaveLength(1)
    expect(store.filteredInfoList[0].status).toBe('draft')

    // 测试关键词搜索
    store.searchParams.status = 'published'
    store.searchParams.keyword = '政策'
    expect(store.filteredInfoList).toHaveLength(1)
    expect(store.filteredInfoList[0].title).toContain('政策')
  })

  it('应该正确计算信息统计', () => {
    const store = useInfoStore()

    store.infoList = [
      {
        id: '1',
        type: 'announcement',
        status: 'published',
      },
      {
        id: '2',
        type: 'policy',
        status: 'published',
      },
      {
        id: '3',
        type: 'announcement',
        status: 'draft',
      },
    ] as InfoItem[]

    const stats = store.infoStats
    expect(stats.total).toBe(3)
    expect(stats.announcement).toBe(2)
    expect(stats.policy).toBe(1)
    expect(stats.notification).toBe(0)
    expect(stats.published).toBe(2)
    expect(stats.draft).toBe(1)
  })

  it('应该正确获取信息列表', async () => {
    const store = useInfoStore()

    // 执行获取信息列表
    const result = await store.fetchInfoList()

    expect(store.isLoading).toBe(false)
    expect(store.infoList.length).toBeGreaterThan(0) // 模拟数据会生成多条记录
    expect(store.pagination.total).toBeGreaterThan(0)
    expect(result).toBeDefined()
  })

  it('应该正确获取信息详情', async () => {
    const store = useInfoStore()
    const testId = 'test-id'

    const result = await store.fetchInfoDetail(testId)

    expect(store.isLoadingDetail).toBe(false)
    expect(store.currentInfo).toBeDefined()
    expect(store.currentInfo?.id).toBe(testId)
    expect(result).toBeDefined()
  })

  it('应该正确更新搜索参数', () => {
    const store = useInfoStore()

    store.updateSearchParams({
      keyword: '测试关键词',
      category: 'announcement',
    })

    expect(store.searchParams.keyword).toBe('测试关键词')
    expect(store.searchParams.category).toBe('announcement')
    expect(store.pagination.page).toBe(1) // 应该重置到第一页
  })

  it('应该正确重置搜索', () => {
    const store = useInfoStore()

    // 先设置一些搜索参数
    store.searchParams.keyword = '测试'
    store.searchParams.category = 'announcement'
    store.searchParams.status = 'draft'
    store.pagination.page = 3

    // 重置搜索
    store.resetSearch()

    expect(store.searchParams.keyword).toBe('')
    expect(store.searchParams.category).toBe('')
    expect(store.searchParams.status).toBe('published')
    expect(store.pagination.page).toBe(1)
  })

  it('应该正确清除当前信息', () => {
    const store = useInfoStore()

    // 先设置当前信息
    store.currentInfo = {
      id: 'test',
      title: '测试',
      content: '内容',
      type: 'announcement',
      status: 'published',
      author: '作者',
      publishTime: '2024-01-15T10:00:00Z',
      views: 0,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z',
    }

    store.clearCurrentInfo()

    expect(store.currentInfo).toBeNull()
  })
})
