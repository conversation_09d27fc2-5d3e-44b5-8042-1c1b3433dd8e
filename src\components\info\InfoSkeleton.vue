<script setup lang="ts">
// 信息列表加载骨架屏组件
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
    <!-- 头部骨架 -->
    <div class="flex items-start justify-between mb-4">
      <div class="flex-1">
        <!-- 标题和类型骨架 -->
        <div class="flex items-center space-x-3 mb-2">
          <div class="h-6 bg-gray-200 rounded w-2/3"></div>
          <div class="h-5 bg-gray-200 rounded-full w-16"></div>
        </div>

        <!-- 内容预览骨架 -->
        <div class="space-y-2 mb-3">
          <div class="h-4 bg-gray-200 rounded w-full"></div>
          <div class="h-4 bg-gray-200 rounded w-4/5"></div>
          <div class="h-4 bg-gray-200 rounded w-3/5"></div>
        </div>

        <!-- 元信息骨架 -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-1">
            <div class="h-4 w-4 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-20"></div>
          </div>
          <div class="flex items-center space-x-1">
            <div class="h-4 w-4 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-16"></div>
          </div>
          <div class="flex items-center space-x-1">
            <div class="h-4 w-4 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-12"></div>
          </div>
        </div>
      </div>

      <!-- 右侧图标骨架 -->
      <div class="flex-shrink-0 ml-4">
        <div class="h-8 w-8 bg-gray-200 rounded"></div>
      </div>
    </div>

    <!-- 底部附件区域骨架 -->
    <div class="flex items-center justify-between pt-3 border-t border-gray-100">
      <div class="flex items-center space-x-2">
        <div class="h-4 bg-gray-200 rounded w-8"></div>
        <div class="flex space-x-1">
          <div class="h-6 bg-gray-200 rounded w-20"></div>
          <div class="h-6 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
      
      <div class="h-4 bg-gray-200 rounded w-20"></div>
    </div>
  </div>
</template>

<style scoped>
/* 骨架屏动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
