<script setup lang="ts">
import { ref, computed } from 'vue'
import { useInfo } from '@/composables/useInfo'
import type { InfoItem } from '@/types'

const {
  searchKeyword,
  selectedCategory,
  selectedStatus,
  updateSearch,
  resetSearch,
  getTypeLabel
} = useInfo()

// 本地搜索关键词（用于输入框）
const localKeyword = ref(searchKeyword.value)

// 筛选选项
const categoryOptions = [
  { value: '', label: '全部类型' },
  { value: 'announcement', label: '公告' },
  { value: 'policy', label: '政策' },
  { value: 'notification', label: '通知' }
]

const statusOptions = [
  { value: '', label: '全部状态' },
  { value: 'published', label: '已发布' },
  { value: 'draft', label: '草稿' }
]

// 计算属性
const hasActiveFilters = computed(() => {
  return searchKeyword.value || selectedCategory.value || selectedStatus.value !== 'published'
})

// 方法
function handleSearch() {
  updateSearch({ keyword: localKeyword.value })
}

function handleCategoryChange(category: string) {
  updateSearch({ category: category as InfoItem['type'] | '' })
}

function handleStatusChange(status: string) {
  updateSearch({ status: status as InfoItem['status'] | '' })
}

function handleReset() {
  localKeyword.value = ''
  resetSearch()
}

function handleKeywordKeyup(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    handleSearch()
  }
}
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <div class="space-y-4">
      <!-- 搜索框 -->
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
            搜索信息
          </label>
          <div class="relative">
            <input
              id="search"
              v-model="localKeyword"
              type="text"
              placeholder="输入关键词搜索标题、内容或作者..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              @keyup="handleKeywordKeyup"
            >
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span class="text-gray-400">🔍</span>
            </div>
          </div>
        </div>
        
        <div class="flex-shrink-0 pt-6">
          <button
            type="button"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            @click="handleSearch"
          >
            搜索
          </button>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- 信息类型筛选 -->
        <div>
          <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
            信息类型
          </label>
          <select
            id="category"
            :value="selectedCategory"
            class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
            @change="handleCategoryChange(($event.target as HTMLSelectElement).value)"
          >
            <option
              v-for="option in categoryOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>
        </div>

        <!-- 状态筛选 -->
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
            发布状态
          </label>
          <select
            id="status"
            :value="selectedStatus"
            class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
            @change="handleStatusChange(($event.target as HTMLSelectElement).value)"
          >
            <option
              v-for="option in statusOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-end space-x-2">
          <button
            v-if="hasActiveFilters"
            type="button"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            @click="handleReset"
          >
            <span class="mr-1">🔄</span>
            重置
          </button>
        </div>
      </div>

      <!-- 当前筛选条件显示 -->
      <div v-if="hasActiveFilters" class="flex items-center space-x-2 pt-2 border-t border-gray-100">
        <span class="text-sm text-gray-500">当前筛选:</span>
        
        <span
          v-if="searchKeyword"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
        >
          关键词: {{ searchKeyword }}
        </span>
        
        <span
          v-if="selectedCategory"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
        >
          类型: {{ getTypeLabel(selectedCategory as InfoItem['type']) }}
        </span>
        
        <span
          v-if="selectedStatus && selectedStatus !== 'published'"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
        >
          状态: {{ selectedStatus === 'draft' ? '草稿' : selectedStatus }}
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 筛选组件样式 */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}
</style>
