import { request } from './index'
import type { InfoItem, PaginationParams, PaginationResponse, SearchParams } from '@/types'

// 信息中心相关API接口

/**
 * 获取信息列表
 * @param params 查询参数
 */
export function getInfoList(params?: Partial<PaginationParams & SearchParams>): Promise<PaginationResponse<InfoItem>> {
  return request.get('/info/list', { params })
}

/**
 * 获取信息详情
 * @param id 信息ID
 */
export function getInfoDetail(id: string): Promise<InfoItem> {
  return request.get(`/info/${id}`)
}

/**
 * 增加信息浏览量
 * @param id 信息ID
 */
export function incrementInfoViews(id: string): Promise<void> {
  return request.post(`/info/${id}/view`)
}

/**
 * 搜索信息
 * @param params 搜索参数
 */
export function searchInfo(params: SearchParams & Partial<PaginationParams>): Promise<PaginationResponse<InfoItem>> {
  return request.get('/info/search', { params })
}

/**
 * 获取信息统计数据
 */
export function getInfoStats(): Promise<{
  total: number
  announcement: number
  policy: number
  notification: number
  published: number
  draft: number
}> {
  return request.get('/info/stats')
}

/**
 * 下载附件
 * @param attachmentId 附件ID
 */
export function downloadAttachment(attachmentId: string): Promise<Blob> {
  return request.get(`/info/attachment/${attachmentId}/download`, {
    responseType: 'blob'
  })
}

/**
 * 获取热门信息（浏览量最高）
 * @param limit 数量限制
 */
export function getPopularInfo(limit: number = 5): Promise<InfoItem[]> {
  return request.get('/info/popular', { params: { limit } })
}

/**
 * 获取最新信息
 * @param limit 数量限制
 */
export function getLatestInfo(limit: number = 5): Promise<InfoItem[]> {
  return request.get('/info/latest', { params: { limit } })
}

/**
 * 按类型获取信息
 * @param type 信息类型
 * @param params 分页参数
 */
export function getInfoByType(
  type: InfoItem['type'], 
  params?: Partial<PaginationParams>
): Promise<PaginationResponse<InfoItem>> {
  return request.get(`/info/type/${type}`, { params })
}

/**
 * 获取信息分类统计
 */
export function getInfoCategoryStats(): Promise<{
  [key in InfoItem['type']]: number
}> {
  return request.get('/info/category-stats')
}
