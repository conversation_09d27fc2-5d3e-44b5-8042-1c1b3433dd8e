<script setup lang="ts">
import { useRouter } from 'vue-router'
import AppButton from '@/components/ui/AppButton.vue'

const router = useRouter()

function goHome() {
  router.push('/')
}

function goBack() {
  router.back()
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="text-center">
      <div class="text-9xl mb-4">🔍</div>
      <h1 class="text-4xl font-bold text-gray-900 mb-4">404</h1>
      <h2 class="text-xl text-gray-600 mb-8">页面未找到</h2>
      <p class="text-gray-500 mb-8">
        抱歉，您访问的页面不存在或已被移除。
      </p>
      <div class="space-x-4">
        <AppButton @click="goBack" variant="secondary">
          返回上页
        </AppButton>
        <AppButton @click="goHome">
          回到首页
        </AppButton>
      </div>
    </div>
  </div>
</template>
