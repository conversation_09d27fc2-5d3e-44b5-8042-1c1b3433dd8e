<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'

interface MenuItem {
  name: string
  path: string
  icon: string
  permission?: string
}

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const menuItems: MenuItem[] = [
  {
    name: '信息中心',
    path: '/info',
    icon: '📢',
    permission: 'info:read'
  },
  {
    name: '学习中心',
    path: '/study',
    icon: '📚',
    permission: 'study:read'
  },
  {
    name: '考试中心',
    path: '/exam',
    icon: '📝',
    permission: 'exam:read'
  },
  {
    name: '个人中心',
    path: '/profile',
    icon: '👤',
    permission: 'profile:read'
  }
]

const filteredMenuItems = computed(() => {
  return menuItems.filter(item => {
    if (!item.permission) return true
    return authStore.hasPermission(item.permission)
  })
})

function isActive(path: string): boolean {
  return route.path.startsWith(path)
}

function handleLogout() {
  authStore.clearAuth()
  router.push('/login')
}
</script>

<template>
  <aside class="w-64 bg-white shadow-lg h-full flex flex-col">
    <!-- Logo区域 -->
    <div class="p-6 border-b border-gray-200">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-sm">CDC</span>
        </div>
        <div>
          <h1 class="text-lg font-semibold text-gray-900">疾控考试系统</h1>
          <p class="text-xs text-gray-500">医护任职资格考试</p>
        </div>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="flex-1 p-4">
      <ul class="space-y-2">
        <li v-for="item in filteredMenuItems" :key="item.path">
          <router-link
            :to="item.path"
            :class="[
              'flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors',
              isActive(item.path)
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
            ]"
          >
            <span class="text-lg">{{ item.icon }}</span>
            <span>{{ item.name }}</span>
          </router-link>
        </li>
      </ul>
    </nav>

    <!-- 用户信息和退出 -->
    <div class="p-4 border-t border-gray-200">
      <div v-if="authStore.user" class="mb-4">
        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
          <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <span class="text-gray-600 text-sm">{{ authStore.user.realName?.[0] || '用' }}</span>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 truncate">
              {{ authStore.user.realName || authStore.user.username }}
            </p>
            <p class="text-xs text-gray-500 truncate">{{ authStore.user.workUnit }}</p>
          </div>
        </div>
      </div>
      
      <button
        @click="handleLogout"
        class="w-full flex items-center space-x-3 px-4 py-3 text-sm font-medium text-red-700 hover:bg-red-50 rounded-lg transition-colors"
      >
        <span class="text-lg">🚪</span>
        <span>退出登录</span>
      </button>
    </div>
  </aside>
</template>
