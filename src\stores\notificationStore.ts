import { ref } from 'vue'
import { defineStore } from 'pinia'

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  persistent?: boolean
}

export const useNotificationStore = defineStore('notification', () => {
  // 状态
  const notifications = ref<Notification[]>([])

  // 方法
  function addNotification(notification: Omit<Notification, 'id'>) {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
    const newNotification: Notification = {
      id,
      duration: 5000, // 默认5秒
      persistent: false,
      ...notification
    }

    notifications.value.push(newNotification)

    // 如果不是持久化通知，自动移除
    if (!newNotification.persistent && newNotification.duration) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }

    return id
  }

  function removeNotification(id: string) {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  function clearAllNotifications() {
    notifications.value = []
  }

  // 便捷方法
  function success(title: string, message?: string, options?: Partial<Notification>) {
    return addNotification({ type: 'success', title, message, ...options })
  }

  function error(title: string, message?: string, options?: Partial<Notification>) {
    return addNotification({ type: 'error', title, message, persistent: true, ...options })
  }

  function warning(title: string, message?: string, options?: Partial<Notification>) {
    return addNotification({ type: 'warning', title, message, ...options })
  }

  function info(title: string, message?: string, options?: Partial<Notification>) {
    return addNotification({ type: 'info', title, message, ...options })
  }

  return {
    // 状态
    notifications,
    
    // 方法
    addNotification,
    removeNotification,
    clearAllNotifications,
    success,
    error,
    warning,
    info
  }
})
