<script setup lang="ts">
import { useAuthStore } from '@/stores/authStore'

const authStore = useAuthStore()
</script>

<template>
  <div class="space-y-6">
    <!-- 欢迎区域 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            欢迎回来，{{ authStore.user?.realName || authStore.user?.username }}！
          </h1>
          <p class="mt-2 text-gray-600">疾控医护任职资格考试系统为您提供专业的学习和考试服务</p>
        </div>
        <div class="text-6xl">👋</div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <router-link
        to="/info"
        class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
      >
        <div class="flex items-center space-x-4">
          <div class="text-3xl">📢</div>
          <div>
            <h3 class="font-semibold text-gray-900">信息中心</h3>
            <p class="text-sm text-gray-600">查看最新公告和政策</p>
          </div>
        </div>
      </router-link>

      <router-link
        to="/study"
        class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
      >
        <div class="flex items-center space-x-4">
          <div class="text-3xl">📚</div>
          <div>
            <h3 class="font-semibold text-gray-900">学习中心</h3>
            <p class="text-sm text-gray-600">在线学习和练习</p>
          </div>
        </div>
      </router-link>

      <router-link
        to="/exam"
        class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
      >
        <div class="flex items-center space-x-4">
          <div class="text-3xl">📝</div>
          <div>
            <h3 class="font-semibold text-gray-900">考试中心</h3>
            <p class="text-sm text-gray-600">参加在线考试</p>
          </div>
        </div>
      </router-link>

      <router-link
        to="/profile"
        class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
      >
        <div class="flex items-center space-x-4">
          <div class="text-3xl">👤</div>
          <div>
            <h3 class="font-semibold text-gray-900">个人中心</h3>
            <p class="text-sm text-gray-600">管理个人信息</p>
          </div>
        </div>
      </router-link>
    </div>

    <!-- 统计信息 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">系统概览</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">0</div>
          <div class="text-sm text-gray-600">待完成考试</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">0</div>
          <div class="text-sm text-gray-600">已获得证书</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">0</div>
          <div class="text-sm text-gray-600">学习进度</div>
        </div>
      </div>
    </div>
  </div>
</template>
