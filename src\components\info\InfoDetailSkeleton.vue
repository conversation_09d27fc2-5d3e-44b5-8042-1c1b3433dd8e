<script setup lang="ts">
// 信息详情页面加载骨架屏组件
</script>

<template>
  <div class="max-w-4xl mx-auto space-y-6 animate-pulse">
    <!-- 导航栏骨架 -->
    <div class="flex items-center justify-between">
      <div class="h-4 bg-gray-200 rounded w-20"></div>
      <div class="h-4 bg-gray-200 rounded w-24"></div>
    </div>

    <!-- 信息头部骨架 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
      <!-- 标题和类型骨架 -->
      <div class="flex items-start justify-between mb-6">
        <div class="flex-1">
          <div class="flex items-center space-x-3 mb-4">
            <div class="h-8 w-8 bg-gray-200 rounded"></div>
            <div class="h-6 bg-gray-200 rounded-full w-16"></div>
          </div>
          
          <div class="space-y-2">
            <div class="h-8 bg-gray-200 rounded w-3/4"></div>
            <div class="h-8 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>

      <!-- 元信息骨架 -->
      <div class="flex flex-wrap items-center gap-6 pb-6 border-b border-gray-200">
        <div class="flex items-center space-x-2">
          <div class="h-4 w-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded w-32"></div>
        </div>
        <div class="flex items-center space-x-2">
          <div class="h-4 w-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded w-20"></div>
        </div>
        <div class="flex items-center space-x-2">
          <div class="h-4 w-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded w-16"></div>
        </div>
        <div class="flex items-center space-x-2">
          <div class="h-4 w-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded w-12"></div>
        </div>
      </div>
    </div>

    <!-- 信息内容骨架 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
      <div class="space-y-4">
        <!-- 段落骨架 -->
        <div class="space-y-2">
          <div class="h-4 bg-gray-200 rounded w-full"></div>
          <div class="h-4 bg-gray-200 rounded w-5/6"></div>
          <div class="h-4 bg-gray-200 rounded w-4/5"></div>
        </div>
        
        <!-- 标题骨架 -->
        <div class="h-6 bg-gray-200 rounded w-1/3 mt-6"></div>
        
        <!-- 列表骨架 -->
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <div class="h-2 w-2 bg-gray-200 rounded-full"></div>
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
          <div class="flex items-center space-x-2">
            <div class="h-2 w-2 bg-gray-200 rounded-full"></div>
            <div class="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
          <div class="flex items-center space-x-2">
            <div class="h-2 w-2 bg-gray-200 rounded-full"></div>
            <div class="h-4 bg-gray-200 rounded w-4/5"></div>
          </div>
        </div>
        
        <!-- 另一个段落骨架 -->
        <div class="space-y-2 mt-6">
          <div class="h-4 bg-gray-200 rounded w-full"></div>
          <div class="h-4 bg-gray-200 rounded w-3/4"></div>
          <div class="h-4 bg-gray-200 rounded w-5/6"></div>
          <div class="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    </div>

    <!-- 附件列表骨架 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="h-5 bg-gray-200 rounded w-20 mb-4"></div>
      <div class="space-y-3">
        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="h-8 w-8 bg-gray-200 rounded"></div>
            <div>
              <div class="h-4 bg-gray-200 rounded w-32 mb-1"></div>
              <div class="h-3 bg-gray-200 rounded w-16"></div>
            </div>
          </div>
          <div class="h-8 bg-gray-200 rounded w-16"></div>
        </div>
        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="h-8 w-8 bg-gray-200 rounded"></div>
            <div>
              <div class="h-4 bg-gray-200 rounded w-28 mb-1"></div>
              <div class="h-3 bg-gray-200 rounded w-12"></div>
            </div>
          </div>
          <div class="h-8 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
    </div>

    <!-- 底部操作骨架 -->
    <div class="flex items-center justify-between bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="h-4 bg-gray-200 rounded w-32"></div>
      
      <div class="flex space-x-4">
        <div class="h-9 bg-gray-200 rounded w-20"></div>
        <div class="h-9 bg-gray-200 rounded w-20"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 骨架屏动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
