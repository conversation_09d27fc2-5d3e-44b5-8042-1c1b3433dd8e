import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { InfoItem, PaginationParams, PaginationResponse, SearchParams } from '@/types'
import { request } from '@/api'

export const useInfoStore = defineStore('info', () => {
  // 状态
  const infoList = ref<InfoItem[]>([])
  const currentInfo = ref<InfoItem | null>(null)
  const isLoading = ref(false)
  const isLoadingDetail = ref(false)
  const pagination = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  })
  const searchParams = ref<SearchParams>({
    keyword: '',
    category: '',
    status: 'published'
  })

  // 计算属性
  const filteredInfoList = computed(() => {
    let filtered = infoList.value

    // 按类型筛选
    if (searchParams.value.category) {
      filtered = filtered.filter(item => item.type === searchParams.value.category)
    }

    // 按状态筛选
    if (searchParams.value.status) {
      filtered = filtered.filter(item => item.status === searchParams.value.status)
    }

    // 关键词搜索
    if (searchParams.value.keyword) {
      const keyword = searchParams.value.keyword.toLowerCase()
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(keyword) ||
        item.content.toLowerCase().includes(keyword) ||
        item.author.toLowerCase().includes(keyword)
      )
    }

    return filtered
  })

  const infoStats = computed(() => {
    const stats = {
      total: infoList.value.length,
      announcement: 0,
      policy: 0,
      notification: 0,
      published: 0,
      draft: 0
    }

    infoList.value.forEach(item => {
      stats[item.type]++
      stats[item.status]++
    })

    return stats
  })

  // 方法
  async function fetchInfoList(params?: Partial<PaginationParams & SearchParams>) {
    isLoading.value = true
    
    try {
      // 更新搜索参数
      if (params) {
        Object.assign(searchParams.value, params)
        if (params.page) pagination.value.page = params.page
        if (params.pageSize) pagination.value.pageSize = params.pageSize
      }

      // 模拟API调用 - 实际项目中替换为真实API
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const mockData: PaginationResponse<InfoItem> = {
        items: generateMockInfoList(),
        total: 25,
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
        totalPages: Math.ceil(25 / pagination.value.pageSize)
      }

      infoList.value = mockData.items
      pagination.value.total = mockData.total
      pagination.value.totalPages = mockData.totalPages

      return mockData
    } catch (error) {
      console.error('获取信息列表失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  async function fetchInfoDetail(id: string) {
    isLoadingDetail.value = true
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const mockInfo = generateMockInfoDetail(id)
      currentInfo.value = mockInfo
      
      // 增加浏览量
      const index = infoList.value.findIndex(item => item.id === id)
      if (index !== -1) {
        infoList.value[index].views++
      }

      return mockInfo
    } catch (error) {
      console.error('获取信息详情失败:', error)
      throw error
    } finally {
      isLoadingDetail.value = false
    }
  }

  function updateSearchParams(params: Partial<SearchParams>) {
    Object.assign(searchParams.value, params)
    pagination.value.page = 1 // 重置到第一页
  }

  function resetSearch() {
    searchParams.value = {
      keyword: '',
      category: '',
      status: 'published'
    }
    pagination.value.page = 1
  }

  function clearCurrentInfo() {
    currentInfo.value = null
  }

  // 模拟数据生成函数
  function generateMockInfoList(): InfoItem[] {
    const types: InfoItem['type'][] = ['announcement', 'policy', 'notification']
    const authors = ['系统管理员', '政策发布部', '考试中心', '技术支持']
    
    return Array.from({ length: pagination.value.pageSize }, (_, index) => {
      const id = `info_${pagination.value.page}_${index + 1}`
      const type = types[Math.floor(Math.random() * types.length)]
      const typeNames = {
        announcement: '公告',
        policy: '政策',
        notification: '通知'
      }
      
      return {
        id,
        title: `${typeNames[type]}标题 - 第${pagination.value.page}页第${index + 1}条`,
        content: `这是一条${typeNames[type]}的详细内容，包含了重要的信息和说明...`,
        type,
        status: 'published',
        publishTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        author: authors[Math.floor(Math.random() * authors.length)],
        views: Math.floor(Math.random() * 1000),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    })
  }

  function generateMockInfoDetail(id: string): InfoItem {
    const baseInfo = infoList.value.find(item => item.id === id)
    if (baseInfo) {
      return {
        ...baseInfo,
        content: `
          <h2>详细内容</h2>
          <p>这是${baseInfo.title}的详细内容。</p>
          <p>本信息包含了重要的政策解读和实施细则，请仔细阅读。</p>
          <h3>主要内容</h3>
          <ul>
            <li>重要事项一：关于考试安排的说明</li>
            <li>重要事项二：关于报名流程的调整</li>
            <li>重要事项三：关于证书发放的通知</li>
          </ul>
          <h3>注意事项</h3>
          <p>请各位考生务必关注相关通知，按时完成相关操作。</p>
        `,
        attachments: [
          {
            id: 'att_1',
            name: '考试安排通知.pdf',
            url: '/files/exam-notice.pdf',
            size: 1024 * 1024,
            type: 'application/pdf'
          },
          {
            id: 'att_2',
            name: '报名表格.xlsx',
            url: '/files/registration-form.xlsx',
            size: 512 * 1024,
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          }
        ]
      }
    }
    
    // 如果没有找到基础信息，返回默认详情
    return {
      id,
      title: '信息详情',
      content: '<p>信息内容加载中...</p>',
      type: 'announcement',
      status: 'published',
      publishTime: new Date().toISOString(),
      author: '系统管理员',
      views: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  return {
    // 状态
    infoList,
    currentInfo,
    isLoading,
    isLoadingDetail,
    pagination,
    searchParams,
    
    // 计算属性
    filteredInfoList,
    infoStats,
    
    // 方法
    fetchInfoList,
    fetchInfoDetail,
    updateSearchParams,
    resetSearch,
    clearCurrentInfo
  }
})
