<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'

const route = useRoute()
const authStore = useAuthStore()

const pageTitle = computed(() => {
  const routeMap: Record<string, string> = {
    '/info': '信息中心',
    '/study': '学习中心',
    '/exam': '考试中心',
    '/profile': '个人中心',
    '/': '首页'
  }
  
  // 查找匹配的路由
  for (const [path, title] of Object.entries(routeMap)) {
    if (route.path.startsWith(path) && path !== '/') {
      return title
    }
  }
  
  return routeMap['/'] || '疾控医护任职资格考试系统'
})

const currentTime = computed(() => {
  return new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    weekday: 'long'
  })
})
</script>

<template>
  <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
    <div class="flex items-center justify-between">
      <!-- 页面标题 -->
      <div class="flex items-center space-x-4">
        <h2 class="text-xl font-semibold text-gray-900">{{ pageTitle }}</h2>
        <nav class="hidden md:flex items-center space-x-2 text-sm text-gray-500">
          <router-link to="/" class="hover:text-gray-700">首页</router-link>
          <span>/</span>
          <span class="text-gray-900">{{ pageTitle }}</span>
        </nav>
      </div>

      <!-- 右侧信息 -->
      <div class="flex items-center space-x-6">
        <!-- 当前时间 -->
        <div class="hidden lg:block text-sm text-gray-600">
          {{ currentTime }}
        </div>

        <!-- 用户信息 -->
        <div v-if="authStore.user" class="flex items-center space-x-3">
          <div class="text-right">
            <p class="text-sm font-medium text-gray-900">
              {{ authStore.user.realName || authStore.user.username }}
            </p>
            <p class="text-xs text-gray-500">{{ authStore.user.position }}</p>
          </div>
          <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span class="text-blue-600 text-sm font-medium">
              {{ authStore.user.realName?.[0] || '用' }}
            </span>
          </div>
        </div>

        <!-- 通知图标 (预留) -->
        <button class="relative p-2 text-gray-400 hover:text-gray-600 transition-colors">
          <span class="text-lg">🔔</span>
          <!-- 通知小红点 -->
          <span class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
        </button>
      </div>
    </div>
  </header>
</template>
