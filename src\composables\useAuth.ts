import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { useNotificationStore } from '@/stores/notificationStore'

export function useAuth() {
  const authStore = useAuthStore()
  const notificationStore = useNotificationStore()
  const router = useRouter()

  // 计算属性
  const isAuthenticated = computed(() => authStore.isAuthenticated)
  const user = computed(() => authStore.user)
  const isLoading = computed(() => authStore.isLoading)

  // 权限检查
  function hasPermission(permission: string): boolean {
    return authStore.hasPermission(permission)
  }

  function hasAnyPermission(permissions: string[]): boolean {
    return authStore.hasAnyPermission(permissions)
  }

  function requireAuth(): boolean {
    if (!isAuthenticated.value) {
      notificationStore.warning('请先登录', '您需要登录后才能访问此功能')
      router.push('/login')
      return false
    }
    return true
  }

  function requirePermission(permission: string): boolean {
    if (!requireAuth()) return false
    
    if (!hasPermission(permission)) {
      notificationStore.error('权限不足', '您没有权限访问此功能')
      return false
    }
    return true
  }

  function requireAnyPermission(permissions: string[]): boolean {
    if (!requireAuth()) return false
    
    if (!hasAnyPermission(permissions)) {
      notificationStore.error('权限不足', '您没有权限访问此功能')
      return false
    }
    return true
  }

  // 登出
  function logout() {
    authStore.clearAuth()
    notificationStore.success('退出成功', '您已安全退出系统')
    router.push('/login')
  }

  // 初始化认证状态
  function initAuth() {
    authStore.initAuth()
  }

  return {
    // 状态
    isAuthenticated,
    user,
    isLoading,
    
    // 方法
    hasPermission,
    hasAnyPermission,
    requireAuth,
    requirePermission,
    requireAnyPermission,
    logout,
    initAuth
  }
}
