<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import AppLayout from '@/components/layout/AppLayout.vue'
import AppNotification from '@/components/ui/AppNotification.vue'

const route = useRoute()
const authStore = useAuthStore()

// 不需要认证的路由
const publicRoutes = ['/login', '/register', '/forgot-password']

// 判断是否需要显示主布局
const shouldShowLayout = computed(() => {
  return authStore.isAuthenticated && !publicRoutes.includes(route.path)
})

onMounted(() => {
  // 初始化认证状态
  authStore.initAuth()
})
</script>

<template>
  <!-- 已登录用户的主布局 -->
  <AppLayout v-if="shouldShowLayout" />

  <!-- 未登录用户的简单布局 -->
  <div v-else class="min-h-screen bg-gray-50">
    <RouterView />
  </div>

  <!-- 全局通知（始终显示） -->
  <AppNotification />
</template>
