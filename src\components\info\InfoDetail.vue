<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useInfoDetail } from '@/composables/useInfo'
import type { InfoItem } from '@/types'
import InfoDetailSkeleton from './InfoDetailSkeleton.vue'
import InfoAttachments from './InfoAttachments.vue'

const route = useRoute()
const router = useRouter()

// 获取信息ID
const infoId = computed(() => route.params.id as string)

// 使用信息详情 composable
const {
  infoDetail,
  isLoading,
  error,
  trackView
} = useInfoDetail(infoId.value)

// 计算属性
const typeIcon = computed(() => {
  if (!infoDetail.value) return '📄'
  const icons = {
    announcement: '📢',
    policy: '📋',
    notification: '📝'
  }
  return icons[infoDetail.value.type] || '📄'
})

const typeLabel = computed(() => {
  if (!infoDetail.value) return ''
  const labels = {
    announcement: '公告',
    policy: '政策',
    notification: '通知'
  }
  return labels[infoDetail.value.type] || infoDetail.value.type
})

const typeColor = computed(() => {
  if (!infoDetail.value) return 'bg-gray-100 text-gray-800'
  const colors = {
    announcement: 'bg-blue-100 text-blue-800',
    policy: 'bg-green-100 text-green-800',
    notification: 'bg-yellow-100 text-yellow-800'
  }
  return colors[infoDetail.value.type] || 'bg-gray-100 text-gray-800'
})

const formattedDate = computed(() => {
  if (!infoDetail.value) return ''
  return new Date(infoDetail.value.publishTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    weekday: 'long'
  })
})

const hasAttachments = computed(() => {
  return infoDetail.value?.attachments && infoDetail.value.attachments.length > 0
})

// 方法
function goBack() {
  router.back()
}

function goToList() {
  router.push('/info')
}

// 生命周期
onMounted(() => {
  // 记录浏览量
  if (infoId.value) {
    trackView()
  }
})
</script>

<template>
  <div class="max-w-4xl mx-auto">
    <!-- 加载状态 -->
    <InfoDetailSkeleton v-if="isLoading" />

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <div class="text-6xl mb-4">❌</div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
      <p class="text-gray-500 mb-4">无法加载信息详情，请稍后重试</p>
      <div class="space-x-4">
        <button
          type="button"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          @click="goBack"
        >
          返回上页
        </button>
        <button
          type="button"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
          @click="goToList"
        >
          返回列表
        </button>
      </div>
    </div>

    <!-- 信息详情内容 -->
    <div v-else-if="infoDetail" class="space-y-6">
      <!-- 导航栏 -->
      <div class="flex items-center justify-between">
        <button
          type="button"
          class="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 transition-colors"
          @click="goBack"
        >
          ← 返回上页
        </button>
        
        <button
          type="button"
          class="inline-flex items-center text-sm text-blue-600 hover:text-blue-700 transition-colors"
          @click="goToList"
        >
          返回信息列表
        </button>
      </div>

      <!-- 信息头部 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <!-- 标题和类型 -->
        <div class="flex items-start justify-between mb-6">
          <div class="flex-1">
            <div class="flex items-center space-x-3 mb-4">
              <span class="text-3xl">{{ typeIcon }}</span>
              <span 
                :class="typeColor"
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
              >
                {{ typeLabel }}
              </span>
            </div>
            
            <h1 class="text-3xl font-bold text-gray-900 leading-tight">
              {{ infoDetail.title }}
            </h1>
          </div>
        </div>

        <!-- 元信息 -->
        <div class="flex flex-wrap items-center gap-6 text-sm text-gray-600 pb-6 border-b border-gray-200">
          <div class="flex items-center space-x-2">
            <span>📅</span>
            <span>发布时间: {{ formattedDate }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span>👤</span>
            <span>发布者: {{ infoDetail.author }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span>👁️</span>
            <span>浏览量: {{ infoDetail.views }}</span>
          </div>
          <div v-if="hasAttachments" class="flex items-center space-x-2">
            <span>📎</span>
            <span>附件: {{ infoDetail.attachments?.length }} 个</span>
          </div>
        </div>
      </div>

      <!-- 信息内容 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div 
          class="prose prose-lg max-w-none"
          v-html="infoDetail.content"
        ></div>
      </div>

      <!-- 附件列表 -->
      <InfoAttachments 
        v-if="hasAttachments"
        :attachments="infoDetail.attachments!"
      />

      <!-- 底部操作 -->
      <div class="flex items-center justify-between bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="text-sm text-gray-500">
          最后更新: {{ new Date(infoDetail.updatedAt).toLocaleString('zh-CN') }}
        </div>
        
        <div class="space-x-4">
          <button
            type="button"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            @click="goBack"
          >
            返回上页
          </button>
          <button
            type="button"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            @click="goToList"
          >
            返回列表
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 富文本内容样式 */
:deep(.prose) {
  @apply text-gray-800 leading-relaxed;
}

:deep(.prose h1) {
  @apply text-2xl font-bold text-gray-900 mt-8 mb-4;
}

:deep(.prose h2) {
  @apply text-xl font-semibold text-gray-900 mt-6 mb-3;
}

:deep(.prose h3) {
  @apply text-lg font-medium text-gray-900 mt-4 mb-2;
}

:deep(.prose p) {
  @apply mb-4;
}

:deep(.prose ul) {
  @apply list-disc list-inside mb-4 space-y-1;
}

:deep(.prose ol) {
  @apply list-decimal list-inside mb-4 space-y-1;
}

:deep(.prose li) {
  @apply text-gray-700;
}

:deep(.prose a) {
  @apply text-blue-600 hover:text-blue-700 underline;
}

:deep(.prose blockquote) {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4;
}

:deep(.prose code) {
  @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm;
}

:deep(.prose pre) {
  @apply bg-gray-100 p-4 rounded-lg overflow-x-auto my-4;
}
</style>
