import { request } from './index'
import type { Exam, ExamRecord, Question, PaginationParams, PaginationResponse } from '@/types'

// 考试中心相关API接口

/**
 * 获取考试列表
 * @param params 查询参数
 */
export function getExamList(
  params?: Partial<
    PaginationParams & {
      status?: Exam['status']
      type?: Exam['type']
      keyword?: string
    }
  >,
): Promise<PaginationResponse<Exam>> {
  return request.get('/exams', { params })
}

/**
 * 获取考试详情
 * @param examId 考试ID
 */
export function getExamDetail(examId: string): Promise<Exam> {
  return request.get(`/exams/${examId}`)
}

/**
 * 获取用户考试记录
 * @param params 查询参数
 */
export function getUserExamRecords(
  params?: Partial<
    PaginationParams & {
      status?: ExamRecord['status']
      examId?: string
    }
  >,
): Promise<PaginationResponse<ExamRecord>> {
  return request.get('/exams/records', { params })
}

/**
 * 获取考试记录详情
 * @param recordId 考试记录ID
 */
export function getExamRecordDetail(recordId: string): Promise<ExamRecord> {
  return request.get(`/exams/records/${recordId}`)
}

/**
 * 报名线下考试
 * @param examId 考试ID
 * @param sessionId 场次ID
 */
export function registerOfflineExam(examId: string, sessionId: string): Promise<ExamRecord> {
  return request.post(`/exams/${examId}/register`, { sessionId })
}

/**
 * 取消线下考试报名
 * @param recordId 考试记录ID
 */
export function cancelExamRegistration(recordId: string): Promise<void> {
  return request.delete(`/exams/records/${recordId}/registration`)
}

/**
 * 获取线下考试场次
 * @param examId 考试ID
 */
export function getExamSessions(examId: string): Promise<
  Array<{
    id: string
    examId: string
    date: string
    startTime: string
    endTime: string
    location: string
    capacity: number
    registered: number
    available: number
    status: 'open' | 'full' | 'closed'
  }>
> {
  return request.get(`/exams/${examId}/sessions`)
}

/**
 * 开始线上考试
 * @param examId 考试ID
 */
export function startOnlineExam(examId: string): Promise<{
  recordId: string
  examRecord: ExamRecord
  questions: Question[]
  timeLimit: number // 考试时长（分钟）
  startTime: string
}> {
  return request.post(`/exams/${examId}/start`)
}

/**
 * 获取考试题目
 * @param recordId 考试记录ID
 */
export function getExamQuestions(recordId: string): Promise<{
  questions: Question[]
  timeRemaining: number // 剩余时间（秒）
  currentAnswers: Record<string, string | string[]>
}> {
  return request.get(`/exams/records/${recordId}/questions`)
}

/**
 * 提交单题答案
 * @param recordId 考试记录ID
 * @param questionId 题目ID
 * @param answer 答案
 */
export function submitExamAnswer(
  recordId: string,
  questionId: string,
  answer: string | string[],
): Promise<{
  success: boolean
  timeRemaining: number
}> {
  return request.post(`/exams/records/${recordId}/answers`, {
    questionId,
    answer,
  })
}

/**
 * 提交整份考试
 * @param recordId 考试记录ID
 * @param answers 所有答案
 */
export function submitExam(
  recordId: string,
  answers: Record<string, string | string[]>,
): Promise<{
  score: number
  passed: boolean
  correctAnswers: number
  totalQuestions: number
  completedAt: string
}> {
  return request.post(`/exams/records/${recordId}/submit`, { answers })
}

/**
 * 暂存考试进度
 * @param recordId 考试记录ID
 * @param answers 当前答案
 */
export function saveExamProgress(
  recordId: string,
  answers: Record<string, string | string[]>,
): Promise<void> {
  return request.put(`/exams/records/${recordId}/progress`, { answers })
}

/**
 * 获取考试统计
 */
export function getExamStats(): Promise<{
  totalExams: number
  completedExams: number
  passedExams: number
  averageScore: number
  upcomingExams: number
  certificates: number
}> {
  return request.get('/exams/stats')
}

/**
 * 检查考试资格
 * @param examId 考试ID
 */
export function checkExamEligibility(examId: string): Promise<{
  eligible: boolean
  reason?: string
  requirements?: string[]
  missingRequirements?: string[]
}> {
  return request.get(`/exams/${examId}/eligibility`)
}

/**
 * 获取考试须知
 * @param examId 考试ID
 */
export function getExamInstructions(examId: string): Promise<{
  title: string
  content: string
  rules: string[]
  requirements: string[]
  duration: number
  totalQuestions: number
  passScore: number
}> {
  return request.get(`/exams/${examId}/instructions`)
}

/**
 * 记录防作弊事件
 * @param recordId 考试记录ID
 * @param eventType 事件类型
 * @param details 事件详情
 */
export function reportAntiCheatEvent(
  recordId: string,
  eventType:
    | 'tab_switch'
    | 'fullscreen_exit'
    | 'copy_paste'
    | 'right_click'
    | 'suspicious_activity',
  details?: Record<string, any>,
): Promise<void> {
  return request.post(`/exams/records/${recordId}/anti-cheat`, {
    eventType,
    details,
    timestamp: new Date().toISOString(),
  })
}

/**
 * 获取考试环境检查
 * @param examId 考试ID
 */
export function checkExamEnvironment(examId: string): Promise<{
  browserSupported: boolean
  screenResolution: { width: number; height: number }
  fullscreenSupported: boolean
  requirements: {
    minScreenWidth: number
    minScreenHeight: number
    requiredBrowser: string[]
    fullscreenRequired: boolean
  }
  warnings: string[]
}> {
  return request.post(`/exams/${examId}/environment-check`, {
    userAgent: navigator.userAgent,
    screenWidth: screen.width,
    screenHeight: screen.height,
    timestamp: new Date().toISOString(),
  })
}

/**
 * 延长考试时间（特殊情况）
 * @param recordId 考试记录ID
 * @param reason 延长原因
 * @param minutes 延长分钟数
 */
export function requestTimeExtension(
  recordId: string,
  reason: string,
  minutes: number,
): Promise<{
  approved: boolean
  newEndTime?: string
  message: string
}> {
  return request.post(`/exams/records/${recordId}/extend-time`, {
    reason,
    minutes,
  })
}

/**
 * 获取考试结果详情
 * @param recordId 考试记录ID
 */
export function getExamResult(recordId: string): Promise<{
  record: ExamRecord
  score: number
  passed: boolean
  correctAnswers: number
  wrongAnswers: number
  totalQuestions: number
  accuracy: number
  timeUsed: number // 用时（分钟）
  rank?: number // 排名
  certificate?: {
    id: string
    downloadUrl: string
    issueDate: string
  }
  wrongQuestionAnalysis?: Array<{
    question: Question
    userAnswer: string | string[]
    correctAnswer: string | string[]
    explanation?: string
  }>
}> {
  return request.get(`/exams/records/${recordId}/result`)
}

/**
 * 获取考试中心概览数据（用于主页面）
 */
export function getExamOverview(): Promise<{
  totalExams: number
  upcomingExams: number
  completedExams: number
  passedExams: number
  averageScore: number
  certificates: number
  nextExam?: {
    id: string
    title: string
    startTime: string
    type: 'online' | 'offline'
    status: string
  }
  recentActivity: Array<{
    type: 'exam_completed' | 'exam_registered' | 'certificate_issued'
    title: string
    date: string
    details?: string
  }>
}> {
  return request.get('/exams/overview')
}

/**
 * 获取待考试列表（用于主页面）
 * @param limit 数量限制
 */
export function getUpcomingExams(limit: number = 5): Promise<Exam[]> {
  return request.get('/exams/upcoming', { params: { limit } })
}

/**
 * 获取最近考试记录（用于主页面）
 * @param limit 数量限制
 */
export function getRecentExamRecords(limit: number = 5): Promise<ExamRecord[]> {
  return request.get('/exams/records/recent', { params: { limit } })
}

/**
 * 获取热门考试
 * @param limit 数量限制
 */
export function getPopularExams(limit: number = 6): Promise<Exam[]> {
  return request.get('/exams/popular', { params: { limit } })
}
