<script setup lang="ts">
import { computed } from 'vue'
import type { Attachment } from '@/types'
import { useInfo } from '@/composables/useInfo'

interface Props {
  attachments: Attachment[]
}

const props = defineProps<Props>()

const { downloadAttachment } = useInfo()

// 计算属性
const attachmentCount = computed(() => props.attachments.length)

// 方法
function getFileIcon(type: string): string {
  const iconMap: Record<string, string> = {
    'application/pdf': '📄',
    'application/msword': '📝',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '📝',
    'application/vnd.ms-excel': '📊',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '📊',
    'application/vnd.ms-powerpoint': '📽️',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': '📽️',
    'image/jpeg': '🖼️',
    'image/png': '🖼️',
    'image/gif': '🖼️',
    'text/plain': '📄',
    'application/zip': '🗜️',
    'application/x-rar-compressed': '🗜️'
  }
  
  return iconMap[type] || '📎'
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toUpperCase() || ''
}

function handleDownload(attachment: Attachment) {
  downloadAttachment(attachment.id, attachment.name)
}

function isImageFile(type: string): boolean {
  return type.startsWith('image/')
}

function isPdfFile(type: string): boolean {
  return type === 'application/pdf'
}

function canPreview(type: string): boolean {
  return isImageFile(type) || isPdfFile(type)
}

function handlePreview(attachment: Attachment) {
  if (canPreview(attachment.type)) {
    // 在新窗口中打开预览
    window.open(attachment.url, '_blank')
  }
}
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <!-- 附件标题 -->
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium text-gray-900 flex items-center space-x-2">
        <span>📎</span>
        <span>附件列表</span>
        <span class="text-sm font-normal text-gray-500">({{ attachmentCount }} 个文件)</span>
      </h3>
    </div>

    <!-- 附件列表 -->
    <div class="space-y-3">
      <div
        v-for="attachment in attachments"
        :key="attachment.id"
        class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
      >
        <!-- 文件信息 -->
        <div class="flex items-center space-x-4 flex-1 min-w-0">
          <!-- 文件图标 -->
          <div class="flex-shrink-0">
            <span class="text-2xl">{{ getFileIcon(attachment.type) }}</span>
          </div>

          <!-- 文件详情 -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-2 mb-1">
              <h4 class="text-sm font-medium text-gray-900 truncate">
                {{ attachment.name }}
              </h4>
              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
                {{ getFileExtension(attachment.name) }}
              </span>
            </div>
            <p class="text-sm text-gray-500">
              文件大小: {{ formatFileSize(attachment.size) }}
            </p>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center space-x-2 flex-shrink-0">
          <!-- 预览按钮 -->
          <button
            v-if="canPreview(attachment.type)"
            type="button"
            class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            @click="handlePreview(attachment)"
          >
            <span class="mr-1">👁️</span>
            预览
          </button>

          <!-- 下载按钮 -->
          <button
            type="button"
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            @click="handleDownload(attachment)"
          >
            <span class="mr-1">⬇️</span>
            下载
          </button>
        </div>
      </div>
    </div>

    <!-- 批量下载 -->
    <div v-if="attachmentCount > 1" class="mt-4 pt-4 border-t border-gray-200">
      <button
        type="button"
        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        @click="() => attachments.forEach(att => handleDownload(att))"
      >
        <span class="mr-2">📦</span>
        下载全部附件
      </button>
    </div>

    <!-- 附件说明 -->
    <div class="mt-4 p-3 bg-blue-50 rounded-lg">
      <div class="flex items-start space-x-2">
        <span class="text-blue-500 mt-0.5">ℹ️</span>
        <div class="text-sm text-blue-700">
          <p class="font-medium mb-1">下载说明:</p>
          <ul class="text-xs space-y-1 text-blue-600">
            <li>• 点击"下载"按钮可直接下载文件</li>
            <li>• 支持预览的文件可点击"预览"按钮在新窗口中查看</li>
            <li>• 如遇下载问题，请检查网络连接或联系技术支持</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 附件列表样式 */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

/* 文件名截断样式 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
