<script setup lang="ts">
import { computed } from 'vue'
import { useNotificationStore, type Notification } from '@/stores/notificationStore'

const notificationStore = useNotificationStore()

const notificationClasses = computed(() => {
  return (type: Notification['type']) => {
    const baseClasses = 'p-4 rounded-lg shadow-lg border-l-4 transition-all duration-300 ease-in-out'
    const typeClasses = {
      success: 'bg-green-50 border-green-400 text-green-800',
      error: 'bg-red-50 border-red-400 text-red-800',
      warning: 'bg-yellow-50 border-yellow-400 text-yellow-800',
      info: 'bg-blue-50 border-blue-400 text-blue-800'
    }
    return `${baseClasses} ${typeClasses[type]}`
  }
})

const notificationIcon = computed(() => {
  return (type: Notification['type']) => {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    }
    return icons[type]
  }
})

function closeNotification(id: string) {
  notificationStore.removeNotification(id)
}
</script>

<template>
  <!-- 通知容器 -->
  <teleport to="body">
    <div class="fixed top-4 right-4 z-50 space-y-3 max-w-sm">
      <transition-group
        name="notification"
        tag="div"
        class="space-y-3"
      >
        <div
          v-for="notification in notificationStore.notifications"
          :key="notification.id"
          :class="notificationClasses(notification.type)"
        >
          <div class="flex items-start space-x-3">
            <!-- 图标 -->
            <span class="text-lg flex-shrink-0 mt-0.5">
              {{ notificationIcon(notification.type) }}
            </span>

            <!-- 内容 -->
            <div class="flex-1 min-w-0">
              <h4 class="font-medium">{{ notification.title }}</h4>
              <p v-if="notification.message" class="mt-1 text-sm opacity-90">
                {{ notification.message }}
              </p>
            </div>

            <!-- 关闭按钮 -->
            <button
              @click="closeNotification(notification.id)"
              class="flex-shrink-0 text-lg opacity-60 hover:opacity-100 transition-opacity"
            >
              ✕
            </button>
          </div>
        </div>
      </transition-group>
    </div>
  </teleport>
</template>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
