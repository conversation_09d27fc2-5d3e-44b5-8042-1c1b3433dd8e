<script setup lang="ts">
import { computed } from 'vue'
import type { InfoItem } from '@/types'
import { useInfo } from '@/composables/useInfo'

interface Props {
  info: InfoItem
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: [info: InfoItem]
}>()

const { formatDate, getTypeLabel, getTypeColor } = useInfo()

// 计算属性
const typeClass = computed(() => getTypeColor(props.info.type))
const typeLabel = computed(() => getTypeLabel(props.info.type))
const formattedDate = computed(() => formatDate(props.info.publishTime))
const hasAttachments = computed(() => props.info.attachments && props.info.attachments.length > 0)

// 截取内容预览
const contentPreview = computed(() => {
  const plainText = props.info.content.replace(/<[^>]*>/g, '') // 移除HTML标签
  return plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText
})

// 方法
function handleClick() {
  emit('click', props.info)
}
</script>

<template>
  <div 
    class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
    @click="handleClick"
  >
    <!-- 头部信息 -->
    <div class="flex items-start justify-between mb-4">
      <div class="flex-1">
        <!-- 标题和类型 -->
        <div class="flex items-center space-x-3 mb-2">
          <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
            {{ info.title }}
          </h3>
          <span 
            :class="typeClass"
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
          >
            {{ typeLabel }}
          </span>
        </div>

        <!-- 内容预览 -->
        <p class="text-gray-600 text-sm leading-relaxed mb-3">
          {{ contentPreview }}
        </p>

        <!-- 元信息 -->
        <div class="flex items-center space-x-4 text-sm text-gray-500">
          <div class="flex items-center space-x-1">
            <span>📅</span>
            <span>{{ formattedDate }}</span>
          </div>
          <div class="flex items-center space-x-1">
            <span>👤</span>
            <span>{{ info.author }}</span>
          </div>
          <div class="flex items-center space-x-1">
            <span>👁️</span>
            <span>{{ info.views }} 次浏览</span>
          </div>
          <div v-if="hasAttachments" class="flex items-center space-x-1">
            <span>📎</span>
            <span>{{ info.attachments?.length }} 个附件</span>
          </div>
        </div>
      </div>

      <!-- 右侧操作区域 -->
      <div class="flex-shrink-0 ml-4">
        <div class="text-2xl">
          {{ info.type === 'announcement' ? '📢' : info.type === 'policy' ? '📋' : '📝' }}
        </div>
      </div>
    </div>

    <!-- 底部标签 -->
    <div v-if="hasAttachments" class="flex items-center justify-between pt-3 border-t border-gray-100">
      <div class="flex items-center space-x-2">
        <span class="text-xs text-gray-500">附件:</span>
        <div class="flex space-x-1">
          <span 
            v-for="attachment in info.attachments?.slice(0, 3)" 
            :key="attachment.id"
            class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700"
          >
            📎 {{ attachment.name }}
          </span>
          <span 
            v-if="info.attachments && info.attachments.length > 3"
            class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700"
          >
            +{{ info.attachments.length - 3 }} 更多
          </span>
        </div>
      </div>
      
      <div class="text-xs text-blue-600 font-medium">
        点击查看详情 →
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 卡片悬停效果 */
.cursor-pointer:hover h3 {
  @apply text-blue-600;
}
</style>
